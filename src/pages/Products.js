import { useEffect, useState } from 'react'
import { client } from '../lib/sanityClient'
import { urlFor } from '../lib/imageBuilder'

export default function Products() {
  const [products, setProducts] = useState([])

  useEffect(() => {
    client.fetch(`*[_type == "product"]{
      _id,
      title,
      price,
      image,
      "category": category->title
    }`)
    .then(setProducts)
    .catch(console.error)
  }, [])

  return (
    <div>
      <h2><PERSON><PERSON><PERSON>n<PERSON></h2>
      <ul>
        {products.map(p => (
          <li key={p._id}>
            <h3>{p.title}</h3>
            <p>Kategori: {p.category}</p>
            <p>Fiyat: {p.price}₺</p>
            {p.image && (
              <img 
                src={urlFor(p.image).width(300).height(200).fit('crop').url()} 
                alt={p.title} 
                style={{ objectFit: 'cover' }}
              />
            )}
          </li>
        ))}
      </ul>
    </div>
  )
}
